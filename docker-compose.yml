services:
  # Public API Connector Service
  public-api-connector:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NODE_VERSION: 20
    container_name: public-api-connector
    restart: unless-stopped
    ports:
      - "3000:3000"
    command: "npm "
    environment:
      # Application Configuration
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 3000
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:3000,http://localhost:3001}
      
      # MongoDB Configuration - Retail POS
      RETAIL_POS_MONGODB_URI: ${RETAIL_POS_MONGODB_URI:-mongodb://mongodb:27017/retail_pos}
      RETAIL_POS_MONGODB_MAX_POOL_SIZE: ${RETAIL_POS_MONGODB_MAX_POOL_SIZE:-10}
      RETAIL_POS_MONGODB_MIN_POOL_SIZE: ${RETAIL_POS_MONGODB_MIN_POOL_SIZE:-5}
      RETAIL_POS_MONGODB_MAX_IDLE_TIME_MS: ${RETAIL_POS_MONGODB_MAX_IDLE_TIME_MS:-30000}
      RETAIL_POS_MONGODB_SERVER_SELECTION_TIMEOUT_MS: ${RETAIL_POS_MONGODB_SERVER_SELECTION_TIMEOUT_MS:-5000}
      
      # MongoDB Configuration - Accounting
      ACCOUNTING_MONGODB_URI: ${ACCOUNTING_MONGODB_URI:-mongodb://mongodb:27017/accounting}
      ACCOUNTING_MONGODB_MAX_POOL_SIZE: ${ACCOUNTING_MONGODB_MAX_POOL_SIZE:-10}
      ACCOUNTING_MONGODB_MIN_POOL_SIZE: ${ACCOUNTING_MONGODB_MIN_POOL_SIZE:-5}
      ACCOUNTING_MONGODB_MAX_IDLE_TIME_MS: ${ACCOUNTING_MONGODB_MAX_IDLE_TIME_MS:-30000}
      ACCOUNTING_MONGODB_SERVER_SELECTION_TIMEOUT_MS: ${ACCOUNTING_MONGODB_SERVER_SELECTION_TIMEOUT_MS:-5000}
      
      # MongoDB Configuration - Loyalty
      LOYALTY_MONGODB_URI: ${LOYALTY_MONGODB_URI:-mongodb://mongodb:27017/loyalty}
      LOYALTY_MONGODB_MAX_POOL_SIZE: ${LOYALTY_MONGODB_MAX_POOL_SIZE:-10}
      LOYALTY_MONGODB_MIN_POOL_SIZE: ${LOYALTY_MONGODB_MIN_POOL_SIZE:-5}
      LOYALTY_MONGODB_MAX_IDLE_TIME_MS: ${LOYALTY_MONGODB_MAX_IDLE_TIME_MS:-30000}
      LOYALTY_MONGODB_SERVER_SELECTION_TIMEOUT_MS: ${LOYALTY_MONGODB_SERVER_SELECTION_TIMEOUT_MS:-5000}
      
      # Kafka Configuration
      KAFKA_BROKERS: ${KAFKA_BROKERS:-kafka:9092}
      KAFKA_CLIENT_ID: ${KAFKA_CLIENT_ID:-public-api-connector}
      KAFKA_GROUP_ID: ${KAFKA_GROUP_ID:-public-api-connector-group}
      KAFKA_SESSION_TIMEOUT: ${KAFKA_SESSION_TIMEOUT:-30000}
      KAFKA_HEARTBEAT_INTERVAL: ${KAFKA_HEARTBEAT_INTERVAL:-3000}
      KAFKA_MAX_WAIT_TIME: ${KAFKA_MAX_WAIT_TIME:-5000}
      KAFKA_RETRY_ATTEMPTS: ${KAFKA_RETRY_ATTEMPTS:-5}
      KAFKA_RETRY_DELAY: ${KAFKA_RETRY_DELAY:-1000}
      
      # External API Configuration - Retail POS
      RETAIL_POS_API_BASE_URL: ${RETAIL_POS_API_BASE_URL:-http://retail-pos-service:8001/api}
      RETAIL_POS_API_TIMEOUT: ${RETAIL_POS_API_TIMEOUT:-30000}
      RETAIL_POS_API_RETRIES: ${RETAIL_POS_API_RETRIES:-3}
      RETAIL_POS_API_RETRY_DELAY: ${RETAIL_POS_API_RETRY_DELAY:-1000}
      RETAIL_POS_API_AUTH_TYPE: ${RETAIL_POS_API_AUTH_TYPE:-bearer}
      RETAIL_POS_API_AUTH_TOKEN: ${RETAIL_POS_API_AUTH_TOKEN}
      
      # External API Configuration - Accounting
      ACCOUNTING_API_BASE_URL: ${ACCOUNTING_API_BASE_URL:-http://accounting-service:8002/api}
      ACCOUNTING_API_TIMEOUT: ${ACCOUNTING_API_TIMEOUT:-30000}
      ACCOUNTING_API_RETRIES: ${ACCOUNTING_API_RETRIES:-3}
      ACCOUNTING_API_RETRY_DELAY: ${ACCOUNTING_API_RETRY_DELAY:-1000}
      ACCOUNTING_API_AUTH_TYPE: ${ACCOUNTING_API_AUTH_TYPE:-bearer}
      ACCOUNTING_API_AUTH_TOKEN: ${ACCOUNTING_API_AUTH_TOKEN}
      
      # External API Configuration - Loyalty
      LOYALTY_API_BASE_URL: ${LOYALTY_API_BASE_URL:-http://loyalty-service:8003/api}
      LOYALTY_API_TIMEOUT: ${LOYALTY_API_TIMEOUT:-30000}
      LOYALTY_API_RETRIES: ${LOYALTY_API_RETRIES:-3}
      LOYALTY_API_RETRY_DELAY: ${LOYALTY_API_RETRY_DELAY:-1000}
      LOYALTY_API_AUTH_TYPE: ${LOYALTY_API_AUTH_TYPE:-bearer}
      LOYALTY_API_AUTH_TOKEN: ${LOYALTY_API_AUTH_TOKEN}
      
      # Circuit Breaker Configuration
      CIRCUIT_BREAKER_ENABLED: ${CIRCUIT_BREAKER_ENABLED:-true}
      CIRCUIT_BREAKER_FAILURE_THRESHOLD: ${CIRCUIT_BREAKER_FAILURE_THRESHOLD:-5}
      CIRCUIT_BREAKER_RESET_TIMEOUT: ${CIRCUIT_BREAKER_RESET_TIMEOUT:-60000}
      
      # Rate Limiting Configuration
      THROTTLE_DEFAULT_TTL: ${THROTTLE_DEFAULT_TTL:-60000}
      THROTTLE_DEFAULT_LIMIT: ${THROTTLE_DEFAULT_LIMIT:-100}
      THROTTLE_RETAIL_POS_TTL: ${THROTTLE_RETAIL_POS_TTL:-60000}
      THROTTLE_RETAIL_POS_LIMIT: ${THROTTLE_RETAIL_POS_LIMIT:-200}
      THROTTLE_ACCOUNTING_TTL: ${THROTTLE_ACCOUNTING_TTL:-60000}
      THROTTLE_ACCOUNTING_LIMIT: ${THROTTLE_ACCOUNTING_LIMIT:-150}
      THROTTLE_LOYALTY_TTL: ${THROTTLE_LOYALTY_TTL:-60000}
      THROTTLE_LOYALTY_LIMIT: ${THROTTLE_LOYALTY_LIMIT:-300}
      
      # Pagination Configuration
      PAGINATION_DEFAULT_LIMIT: ${PAGINATION_DEFAULT_LIMIT:-20}
      PAGINATION_MAX_LIMIT: ${PAGINATION_MAX_LIMIT:-100}
      
      # Logging Configuration
      LOG_LEVEL: ${LOG_LEVEL:-info}
      LOG_FORMAT: ${LOG_FORMAT:-json}
      
      # Security Configuration
      JWT_SECRET: ${JWT_SECRET}
      API_KEY_HEADER: ${API_KEY_HEADER:-X-API-Key}
      API_KEYS: ${API_KEYS}
      
      # Health Check Configuration
      HEALTH_CHECK_TIMEOUT: ${HEALTH_CHECK_TIMEOUT:-5000}
      HEALTH_CHECK_MONGODB_ENABLED: ${HEALTH_CHECK_MONGODB_ENABLED:-true}
      HEALTH_CHECK_KAFKA_ENABLED: ${HEALTH_CHECK_KAFKA_ENABLED:-true}
      
      # Worker Configuration
      WORKER_CONCURRENCY: ${WORKER_CONCURRENCY:-5}
      WORKER_MAX_RETRIES: ${WORKER_MAX_RETRIES:-3}
      WORKER_RETRY_DELAY: ${WORKER_RETRY_DELAY:-5000}
      WORKER_DLQ_ENABLED: ${WORKER_DLQ_ENABLED:-true}
    volumes:
      # Development volume mounts (comment out for production)
      - .:/app
      - /app/node_modules
      - /app/dist
    depends_on:
      mongodb:
        condition: service_healthy
      kafka:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - posxdata

  # MongoDB Service
  mongodb:
    image: mongo:7.0
    container_name: mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-password}
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init:/docker-entrypoint-initdb.d:ro
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - posxdata

  # Kafka Service
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka
    restart: unless-stopped
    ports:
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:29092,PLAINTEXT_HOST://0.0.0.0:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_DELETE_TOPIC_ENABLE: 'true'
    volumes:
      - kafka_data:/var/lib/kafka/data
    depends_on:
      zookeeper:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - posxdata

  # Zookeeper Service (required for Kafka)
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: zookeeper
    restart: unless-stopped
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    healthcheck:
      test: ["CMD", "echo", "ruok", "|", "nc", "localhost", "2181"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - posxdata

  # Redis Service (optional, for advanced throttling)
  redis:
    image: redis:7.2-alpine
    container_name: redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    command: >
      sh -c "
        if [ -n \"$$REDIS_PASSWORD\" ]; then
          redis-server --requirepass $$REDIS_PASSWORD
        else
          redis-server
        fi
      "
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - posxdata

volumes:
  mongodb_data:
    driver: local
  kafka_data:
    driver: local
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local
  redis_data:
    driver: local

networks:
  posxdata:
    external: true
