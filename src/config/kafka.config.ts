import { registerAs } from '@nestjs/config';

export interface KafkaConfig {
  clientId: string;
  brokers: string[];
  groupId: string;
  ssl: boolean;
  sasl?: {
    mechanism: 'plain' | 'scram-sha-256' | 'scram-sha-512';
    username: string;
    password: string;
  };
  connectionTimeout: number;
  requestTimeout: number;
  retry: {
    initialRetryTime: number;
    retries: number;
    maxRetryTime: number;
    factor: number;
    multiplier: number;
    restartOnFailure: (err: Error) => Promise<boolean>;
  };
  consumer: {
    sessionTimeout: number;
    rebalanceTimeout: number;
    heartbeatInterval: number;
    maxBytesPerPartition: number;
    minBytes: number;
    maxBytes: number;
    maxWaitTimeInMs: number;
    allowAutoTopicCreation: boolean;
  };
  producer: {
    maxInFlightRequests: number;
    idempotent: boolean;
    transactionTimeout: number;
    allowAutoTopicCreation: boolean;
  };
}

export default registerAs('kafka', (): KafkaConfig => ({
  clientId: process.env.KAFKA_CLIENT_ID || 'public-api-connector',
  brokers: process.env.KAFKA_BROKERS?.split(',') || ['kafka:9092'],
  groupId: process.env.KAFKA_GROUP_ID || 'public-api-connector-group',
  ssl: process.env.KAFKA_SSL === 'true',
  sasl: process.env.KAFKA_SASL_MECHANISM ? {
    mechanism: process.env.KAFKA_SASL_MECHANISM as 'plain' | 'scram-sha-256' | 'scram-sha-512',
    username: process.env.KAFKA_SASL_USERNAME || '',
    password: process.env.KAFKA_SASL_PASSWORD || '',
  } : undefined,
  connectionTimeout: parseInt(process.env.KAFKA_CONNECTION_TIMEOUT || '3000', 10),
  requestTimeout: parseInt(process.env.KAFKA_REQUEST_TIMEOUT || '30000', 10),
  retry: {
    initialRetryTime: parseInt(process.env.KAFKA_INITIAL_RETRY_TIME || '100', 10),
    retries: parseInt(process.env.KAFKA_RETRIES || '8', 10),
    maxRetryTime: parseInt(process.env.KAFKA_MAX_RETRY_TIME || '30000', 10),
    factor: parseFloat(process.env.KAFKA_RETRY_FACTOR || '0.2'),
    multiplier: parseFloat(process.env.KAFKA_RETRY_MULTIPLIER || '2'),
    restartOnFailure: async (err: Error) => {
      console.error('Kafka connection failed:', err);
      return true;
    },
  },
  consumer: {
    sessionTimeout: parseInt(process.env.KAFKA_SESSION_TIMEOUT || '30000', 10),
    rebalanceTimeout: parseInt(process.env.KAFKA_REBALANCE_TIMEOUT || '60000', 10),
    heartbeatInterval: parseInt(process.env.KAFKA_HEARTBEAT_INTERVAL || '3000', 10),
    maxBytesPerPartition: parseInt(process.env.KAFKA_MAX_BYTES_PER_PARTITION || '1048576', 10),
    minBytes: parseInt(process.env.KAFKA_MIN_BYTES || '1', 10),
    maxBytes: parseInt(process.env.KAFKA_MAX_BYTES || '10485760', 10),
    maxWaitTimeInMs: parseInt(process.env.KAFKA_MAX_WAIT_TIME || '5000', 10),
    allowAutoTopicCreation: process.env.KAFKA_ALLOW_AUTO_TOPIC_CREATION === 'true',
  },
  producer: {
    maxInFlightRequests: parseInt(process.env.KAFKA_MAX_IN_FLIGHT_REQUESTS || '1', 10),
    idempotent: process.env.KAFKA_IDEMPOTENT === 'true',
    transactionTimeout: parseInt(process.env.KAFKA_TRANSACTION_TIMEOUT || '30000', 10),
    allowAutoTopicCreation: process.env.KAFKA_ALLOW_AUTO_TOPIC_CREATION === 'true',
  },
}));
