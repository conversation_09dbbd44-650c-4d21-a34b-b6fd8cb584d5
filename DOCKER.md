# Docker Setup Guide

This guide explains how to run the Public API Connector microservices using Docker Compose.

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- External Docker network `posxdata` must exist

### Create External Network

```bash
docker network create posxdata
```

## Quick Start

### Development Environment

```bash
# Start all services in development mode
docker-compose up -d

# View logs
docker-compose logs -f public-api-connector

# Stop services
docker-compose down
```

### Production Environment

```bash
# Start all services in production mode
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# View logs
docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs -f

# Stop services
docker-compose -f docker-compose.yml -f docker-compose.prod.yml down
```

## Services Overview

### Core Services

| Service | Port | Description |
|---------|------|-------------|
| public-api-connector | 3000 | Main NestJS application |
| mongodb | 27017 | MongoDB database |
| kafka | 9092 | Apache Kafka message broker |
| zookeeper | 2181 | Zookeeper (required for Kafka) |
| redis | 6379 | Redis cache (optional) |

### Development Tools (dev only)

| Service | Port | Description |
|---------|------|-------------|
| kafka-ui | 8080 | Kafka management UI |
| mongo-express | 8081 | MongoDB web interface |
| redis-commander | 8082 | Redis web interface |

## Environment Configuration

### Required Environment Variables

Create a `.env` file in the project root:

```bash
# Copy example environment file
cp .env.example .env

# Edit environment variables
nano .env
```

### Key Environment Variables

```bash
# Application
NODE_ENV=development
PORT=3000

# MongoDB
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=your-secure-password

# Security
JWT_SECRET=your-jwt-secret
API_KEYS=key1,key2,key3

# External APIs
RETAIL_POS_API_AUTH_TOKEN=your-token
ACCOUNTING_API_AUTH_TOKEN=your-token
LOYALTY_API_AUTH_TOKEN=your-token
```

## Database Initialization

MongoDB databases are automatically initialized with:
- Application user with appropriate permissions
- Required collections and indexes
- Basic schema structure

See `docker/mongodb/init/01-init-databases.js` for details.

## Health Checks

All services include health checks:

```bash
# Check service health
docker-compose ps

# View health check logs
docker-compose logs mongodb
```

## Development Workflow

### Hot Reload Development

```bash
# Start in development mode with hot reload
docker-compose up -d

# The application will automatically restart on code changes
# Logs will show compilation progress
docker-compose logs -f public-api-connector
```

### Debugging

```bash
# Start with debug port exposed
docker-compose up -d

# Connect debugger to localhost:9229
# VS Code launch.json example:
{
  "type": "node",
  "request": "attach",
  "name": "Docker Debug",
  "port": 9229,
  "restart": true,
  "localRoot": "${workspaceFolder}",
  "remoteRoot": "/app"
}
```

## Production Deployment

### Prerequisites

```bash
# Create data directories
sudo mkdir -p /opt/posxdata/{mongodb,kafka,zookeeper/{data,logs},redis}
sudo chown -R 1001:1001 /opt/posxdata
```

### Deployment

```bash
# Build and start production services
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build

# Verify deployment
docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps
```

### Monitoring

```bash
# View resource usage
docker stats

# Check logs
docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs --tail=100 -f

# Health check endpoints
curl http://localhost:3000/health
```

## Scaling Services

### Horizontal Scaling

```bash
# Scale the main application
docker-compose up -d --scale public-api-connector=3

# Use load balancer (nginx example)
# Add nginx service to docker-compose.yml
```

### Resource Limits

Production configuration includes resource limits:
- CPU: 0.5-1.0 cores per service
- Memory: 512MB-2GB per service
- Automatic restart policies

## Backup and Recovery

### MongoDB Backup

```bash
# Create backup
docker-compose exec mongodb mongodump --out /data/backup

# Restore backup
docker-compose exec mongodb mongorestore /data/backup
```

### Volume Backup

```bash
# Stop services
docker-compose down

# Backup volumes
docker run --rm -v posxdata_mongodb_data:/data -v $(pwd):/backup alpine tar czf /backup/mongodb-backup.tar.gz -C /data .

# Restore volumes
docker run --rm -v posxdata_mongodb_data:/data -v $(pwd):/backup alpine tar xzf /backup/mongodb-backup.tar.gz -C /data
```

## Troubleshooting

### Common Issues

1. **Network not found**: Create external network
   ```bash
   docker network create posxdata
   ```

2. **Port conflicts**: Check for running services
   ```bash
   netstat -tulpn | grep :3000
   ```

3. **Permission issues**: Fix volume permissions
   ```bash
   sudo chown -R 1001:1001 /opt/posxdata
   ```

4. **Memory issues**: Increase Docker memory limits
   ```bash
   # Docker Desktop: Settings > Resources > Memory
   ```

5. **EBUSY dist directory error**: The custom entrypoint script handles this automatically
   - The `/app/dist` volume mount prevents file locking issues
   - Entrypoint script safely cleans the dist directory on startup
   - If issues persist, try: `docker-compose down -v && docker-compose up -d`

### Logs and Debugging

```bash
# View all logs
docker-compose logs

# Follow specific service logs
docker-compose logs -f public-api-connector

# Debug container
docker-compose exec public-api-connector sh
```

## Security Considerations

- Change default passwords in production
- Use secrets management for sensitive data
- Enable MongoDB authentication
- Configure firewall rules
- Use HTTPS in production
- Regular security updates

## Performance Tuning

### MongoDB

- Adjust WiredTiger cache size
- Configure appropriate indexes
- Monitor query performance

### Kafka

- Tune partition count
- Adjust retention policies
- Monitor consumer lag

### Application

- Configure connection pools
- Adjust worker concurrency
- Enable caching where appropriate
