#!/bin/bash

# Docker entrypoint script for NestJS application
# Handles common development issues like locked dist directory

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}[ENTRYPOINT]${NC} Starting NestJS application..."

# Function to safely clean dist directory
clean_dist() {
    if [ -d "/app/dist" ]; then
        echo -e "${YELLOW}[ENTRYPOINT]${NC} Cleaning dist directory..."
        # Try to remove contents instead of the directory itself
        rm -rf /app/dist/* 2>/dev/null || true
        # If that fails, try to remove and recreate the directory
        if [ $? -ne 0 ]; then
            echo -e "${YELLOW}[ENTRYPOINT]${NC} Attempting alternative cleanup..."
            rm -rf /app/dist 2>/dev/null || true
            mkdir -p /app/dist
        fi
    else
        echo -e "${GREEN}[ENTRYPOINT]${NC} Creating dist directory..."
        mkdir -p /app/dist
    fi
}

# Function to wait for dependencies
wait_for_dependencies() {
    echo -e "${GREEN}[ENTRYPOINT]${NC} Waiting for dependencies..."
    
    # Wait for MongoDB
    if [ -n "$RETAIL_POS_MONGODB_URI" ]; then
        echo -e "${YELLOW}[ENTRYPOINT]${NC} Waiting for MongoDB..."
        while ! nc -z mongodb 27017; do
            sleep 1
        done
        echo -e "${GREEN}[ENTRYPOINT]${NC} MongoDB is ready"
    fi
    
    # Wait for Kafka
    if [ -n "$KAFKA_BROKERS" ]; then
        echo -e "${YELLOW}[ENTRYPOINT]${NC} Waiting for Kafka..."
        while ! nc -z kafka 9092; do
            sleep 1
        done
        echo -e "${GREEN}[ENTRYPOINT]${NC} Kafka is ready"
    fi
}

# Handle different environments
if [ "$NODE_ENV" = "development" ]; then
    echo -e "${GREEN}[ENTRYPOINT]${NC} Running in development mode"
    
    # Clean dist directory for development
    clean_dist
    
    # Wait for dependencies
    wait_for_dependencies
    
    # Install dependencies if node_modules is empty (first run)
    if [ ! -d "/app/node_modules" ] || [ -z "$(ls -A /app/node_modules)" ]; then
        echo -e "${YELLOW}[ENTRYPOINT]${NC} Installing dependencies..."
        npm install
    fi
    
    # Start development server
    echo -e "${GREEN}[ENTRYPOINT]${NC} Starting development server..."
    exec npm run start:dev
    
elif [ "$NODE_ENV" = "production" ]; then
    echo -e "${GREEN}[ENTRYPOINT]${NC} Running in production mode"
    
    # Wait for dependencies
    wait_for_dependencies
    
    # Start production server
    echo -e "${GREEN}[ENTRYPOINT]${NC} Starting production server..."
    exec node dist/main.js
    
else
    echo -e "${GREEN}[ENTRYPOINT]${NC} Running with custom command"
    # Execute the provided command
    exec "$@"
fi
