# Docker Compose override for development environment
# This file is automatically loaded by docker-compose and overrides settings in docker-compose.yml

version: '3.8'

services:
  public-api-connector:
    build:
      target: development  # Use development stage from Dockerfile
    environment:
      NODE_ENV: development
      LOG_LEVEL: debug
    volumes:
      # Enable hot reload for development
      - .:/app
      - /app/node_modules
      - /app/dist

    ports:
      - "3000:3000"
      - "9229:9229"  # Debug port
    
  # Development tools and services
  
  # Kafka UI for development
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    depends_on:
      - kafka
    networks:
      - posxdata

  # MongoDB Express for development
  mongo-express:
    image: mongo-express:latest
    container_name: mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: ${MONGO_ROOT_USERNAME:-admin}
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_ROOT_PASSWORD:-password}
      ME_CONFIG_MONGODB_URL: **************************************/
      ME_CONFIG_BASICAUTH_USERNAME: ${MONGO_EXPRESS_USERNAME:-admin}
      ME_CONFIG_BASICAUTH_PASSWORD: ${MONGO_EXPRESS_PASSWORD:-pass}
    depends_on:
      - mongodb
    networks:
      - posxdata

  # Redis Commander for development
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: redis-commander
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
    depends_on:
      - redis
    networks:
      - posxdata
